/*! tailwindcss v4.1.6 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-50:oklch(97.1% .013 17.38);--color-red-200:oklch(88.5% .062 18.334);--color-red-300:oklch(80.8% .114 19.571);--color-red-400:oklch(70.4% .191 22.216);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-red-800:oklch(44.4% .177 26.899);--color-red-900:oklch(39.6% .141 25.723);--color-orange-50:oklch(98% .016 73.684);--color-orange-200:oklch(90.1% .076 70.697);--color-orange-300:oklch(83.7% .128 66.29);--color-orange-500:oklch(70.5% .213 47.604);--color-orange-600:oklch(64.6% .222 41.116);--color-orange-700:oklch(55.3% .195 38.402);--color-orange-900:oklch(40.8% .123 38.172);--color-yellow-50:oklch(98.7% .026 102.212);--color-yellow-100:oklch(97.3% .071 103.193);--color-yellow-200:oklch(94.5% .129 101.54);--color-yellow-300:oklch(90.5% .182 98.111);--color-yellow-400:oklch(85.2% .199 91.936);--color-yellow-500:oklch(79.5% .184 86.047);--color-yellow-600:oklch(68.1% .162 75.834);--color-yellow-700:oklch(55.4% .135 66.442);--color-yellow-800:oklch(47.6% .114 61.907);--color-yellow-900:oklch(42.1% .095 57.708);--color-green-100:oklch(96.2% .044 156.743);--color-green-200:oklch(92.5% .084 155.995);--color-green-300:oklch(87.1% .15 154.449);--color-green-400:oklch(79.2% .209 151.711);--color-green-500:oklch(72.3% .219 149.579);--color-green-800:oklch(44.8% .119 151.328);--color-green-900:oklch(39.3% .095 152.535);--color-emerald-500:oklch(69.6% .17 162.48);--color-blue-400:oklch(70.7% .165 254.624);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-purple-500:oklch(62.7% .265 303.9);--color-gray-100:oklch(96.7% .003 264.542);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-300:oklch(87.2% .01 258.338);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-800:oklch(27.8% .033 256.848);--color-neutral-100:oklch(97% 0 0);--color-neutral-200:oklch(92.2% 0 0);--color-neutral-300:oklch(87% 0 0);--color-neutral-400:oklch(70.8% 0 0);--color-neutral-600:oklch(43.9% 0 0);--color-neutral-700:oklch(37.1% 0 0);--color-neutral-800:oklch(26.9% 0 0);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xs:20rem;--container-sm:24rem;--container-md:28rem;--container-lg:32rem;--text-xs:calc(var(--vscode-font-size)*.85);--text-xs--line-height:calc(1/.75);--text-sm:calc(var(--vscode-font-size)*.9);--text-sm--line-height:calc(1.25/.875);--text-base:var(--vscode-font-size);--text-base--line-height: 1.5 ;--text-lg:calc(var(--vscode-font-size)*1.1);--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--tracking-wide:.025em;--tracking-widest:.1em;--leading-tight:1.25;--leading-relaxed:1.625;--radius-xs:.125rem;--radius-sm:calc(var(--radius) - 4px);--radius-md:calc(var(--radius) - 2px);--radius-lg:var(--radius);--drop-shadow-md:0 3px 3px #0000001f;--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-sm:8px;--blur-md:12px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-background:var(--background);--color-foreground:var(--foreground);--color-card:var(--card);--color-popover:var(--popover);--color-popover-foreground:var(--popover-foreground);--color-primary:var(--primary);--color-primary-foreground:var(--primary-foreground);--color-secondary:var(--secondary);--color-secondary-foreground:var(--secondary-foreground);--color-muted:var(--muted);--color-muted-foreground:var(--muted-foreground);--color-accent:var(--accent);--color-accent-foreground:var(--accent-foreground);--color-destructive:var(--destructive);--color-destructive-foreground:var(--destructive-foreground);--color-border:var(--border);--color-input:var(--input);--color-ring:var(--ring);--color-vscode-foreground:var(--vscode-foreground);--color-vscode-editor-foreground:var(--vscode-editor-foreground);--color-vscode-editor-background:var(--vscode-editor-background);--color-vscode-editorGroup-border:var(--vscode-editorGroup-border);--color-vscode-editorWarning-foreground:var(--vscode-editorWarning-foreground);--color-vscode-button-foreground:var(--vscode-button-foreground);--color-vscode-button-background:var(--vscode-button-background);--color-vscode-button-secondaryForeground:var(--vscode-button-secondaryForeground);--color-vscode-button-secondaryBackground:var(--vscode-button-secondaryBackground);--color-vscode-dropdown-foreground:var(--vscode-dropdown-foreground);--color-vscode-dropdown-background:var(--vscode-dropdown-background);--color-vscode-dropdown-border:var(--vscode-dropdown-border);--color-vscode-input-foreground:var(--vscode-input-foreground);--color-vscode-input-background:var(--vscode-input-background);--color-vscode-input-border:var(--vscode-input-border,transparent);--color-vscode-focusBorder:var(--vscode-focusBorder);--color-vscode-badge-background:var(--vscode-badge-background);--color-vscode-notifications-foreground:var(--vscode-notifications-foreground);--color-vscode-notifications-background:var(--vscode-notifications-background);--color-vscode-notifications-border:var(--vscode-notifications-border);--color-vscode-descriptionForeground:var(--vscode-descriptionForeground);--color-vscode-errorForeground:var(--vscode-errorForeground);--color-vscode-list-hoverBackground:var(--vscode-list-hoverBackground);--color-vscode-list-activeSelectionBackground:var(--vscode-list-activeSelectionBackground);--color-vscode-list-activeSelectionForeground:var(--vscode-list-activeSelectionForeground);--color-vscode-toolbar-hoverBackground:var(--vscode-toolbar-hoverBackground);--color-vscode-panel-border:var(--vscode-panel-border);--color-vscode-sideBar-foreground:var(--vscode-sideBar-foreground);--color-vscode-sideBar-background:var(--vscode-sideBar-background);--color-vscode-charts-green:var(--vscode-charts-green);--color-vscode-widget-border:var(--vscode-widget-border);--color-vscode-textLink-foreground:var(--vscode-textLink-foreground);--color-vscode-textCodeBlock-background:var(--vscode-textCodeBlock-background);--color-vscode-button-hoverBackground:var(--vscode-button-hoverBackground)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}body{line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1;color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentColor 50%,transparent)}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}:root{--background:var(--vscode-editor-background);--foreground:var(--vscode-editor-foreground);--card:var(--vscode-editor-background);--card-foreground:var(--vscode-editor-foreground);--popover:var(--vscode-menu-background,var(--vscode-editor-background));--popover-foreground:var(--vscode-menu-foreground,var(--vscode-editor-foreground));--primary:var(--vscode-button-background);--primary-foreground:var(--vscode-button-foreground);--secondary:var(--vscode-button-secondaryBackground);--secondary-foreground:var(--vscode-button-secondaryForeground);--muted:var(--vscode-disabledForeground);--muted-foreground:var(--vscode-descriptionForeground);--accent:var(--vscode-list-hoverBackground);--accent-foreground:var(--vscode-list-hoverForeground);--destructive:var(--vscode-errorForeground);--destructive-foreground:var(--vscode-button-foreground);--border:var(--vscode-input-border,transparent);--input:var(--vscode-input-background);--ring:var(--vscode-input-border);--chart-1:var(--vscode-charts-red);--chart-2:var(--vscode-charts-blue);--chart-3:var(--vscode-charts-yellow);--chart-4:var(--vscode-charts-orange);--chart-5:var(--vscode-charts-green);--radius:.5rem}body{--vscode-input-border:var(--border)}}@layer components{.border,.border-r,.border-l,.border-t,.border-b,.border-x,.border-y{border-color:var(--border)}pre,pre code{background-color:var(--vscode-textCodeBlock-background)}code:not(pre code){border:1px solid var(--vscode-input-border,#8080804d)!important;color:var(--vscode-textLink-foreground,#06c)!important;font-family:var(--vscode-editor-font-family,"Consolas","Courier New",monospace)!important;background-color:#0000!important;border-radius:3px!important;padding:1px 4px!important;font-size:.9em!important}.history-item-highlight{text-decoration-line:underline}.hljs .hljs-string,.hljs .hljs-attr,.hljs .hljs-name,.hljs .hljs-property,.hljs .hljs-title,.hljs .hljs-variable,.hljs .hljs-keyword,.hljs .hljs-built_in,.hljs .hljs-literal,.hljs .hljs-number,.hljs .hljs-symbol,.hljs .hljs-meta,.hljs .hljs-tag,.hljs .hljs-selector-tag,.hljs .hljs-selector-id,.hljs .hljs-selector-class,.hljs .hljs-selector-attr,.hljs .hljs-selector-pseudo,.hljs .hljs-type,.hljs .hljs-class,.hljs .hljs-function,.hljs .hljs-params,.hljs .hljs-comment,.hljs .hljs-quote,.hljs .hljs-doctag,.hljs .hljs-formula,.hljs .hljs-section,.hljs .hljs-link,.hljs .hljs-subst,.hljs .hljs-regexp,.hljs .hljs-attribute,.hljs .hljs-addition,.hljs .hljs-deletion,.hljs .hljs-emphasis,.hljs .hljs-strong,.hljs .hljs-template-tag,.hljs .hljs-template-variable,pre code .token.string,pre code .token.property,pre code .token.attr-name,pre code .token.attr-value,pre code .token.keyword,pre code .token.function,pre code .token.class-name,pre code .token.variable,pre code .token.number,pre code .token.boolean,pre code .token.null,pre code .token.undefined,pre code .token.symbol,pre code .token.regex,pre code .token.important,pre code .token.bold,pre code .token.italic,pre code .token.entity,pre code .token.url,pre code .token.selector,pre code .token.atrule,pre code .token.operator,pre code .token.punctuation,pre code .token.comment,pre code .token.prolog,pre code .token.doctype,pre code .token.cdata,pre code span[style*="color:#0969da"],pre code span[style*="color:#0550ae"],pre code span[style*="color:#116329"],pre code span[style*="color:#953800"],pre code span[style*="color:#8250df"],pre code span[style*="color:#cf222e"],pre code span[style*="color:#0a3069"],pre code span[style*="color:#24292f"],pre code span[style*="color:#656d76"],pre code span[style*="color:#1f2328"]{color:var(--vscode-foreground)!important}}@layer utilities{.pointer-events-none{pointer-events:none}.collapse{visibility:collapse}.invisible{visibility:hidden}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.inset-0{inset:calc(var(--spacing)*0)}.-top-\[7px\]{top:-7px}.top-0{top:calc(var(--spacing)*0)}.top-1{top:calc(var(--spacing)*1)}.top-1\/2{top:50%}.top-2{top:calc(var(--spacing)*2)}.top-4{top:calc(var(--spacing)*4)}.top-5{top:calc(var(--spacing)*5)}.top-\[-35px\]{top:-35px}.top-\[50\%\]{top:50%}.top-full{top:100%}.right-0{right:calc(var(--spacing)*0)}.right-1{right:calc(var(--spacing)*1)}.right-2{right:calc(var(--spacing)*2)}.right-4{right:calc(var(--spacing)*4)}.right-5{right:calc(var(--spacing)*5)}.right-\[1px\]{right:1px}.right-\[13px\]{right:13px}.bottom-0{bottom:calc(var(--spacing)*0)}.bottom-2{bottom:calc(var(--spacing)*2)}.bottom-\[1px\]{bottom:1px}.bottom-full{bottom:100%}.left-0{left:calc(var(--spacing)*0)}.left-2{left:calc(var(--spacing)*2)}.left-\[1px\]{left:1px}.left-\[50\%\]{left:50%}.z-1{z-index:1}.z-5{z-index:5}.z-6{z-index:6}.z-10{z-index:10}.z-50{z-index:50}.z-\[1\]{z-index:1}.z-\[3\]{z-index:3}.z-\[1000\]{z-index:1000}.z-\[9999\]{z-index:9999}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.m-0{margin:calc(var(--spacing)*0)}.m-2{margin:calc(var(--spacing)*2)}.m-3{margin:calc(var(--spacing)*3)}.m-5{margin:calc(var(--spacing)*5)}.m-auto{margin:auto}.-mx-1{margin-inline:calc(var(--spacing)*-1)}.mx-1{margin-inline:calc(var(--spacing)*1)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-auto{margin-inline:auto}.my-0{margin-block:calc(var(--spacing)*0)}.my-0\.5{margin-block:calc(var(--spacing)*.5)}.my-1{margin-block:calc(var(--spacing)*1)}.my-2{margin-block:calc(var(--spacing)*2)}.my-3{margin-block:calc(var(--spacing)*3)}.my-4{margin-block:calc(var(--spacing)*4)}.my-5{margin-block:calc(var(--spacing)*5)}.-mt-2{margin-top:calc(var(--spacing)*-2)}.-mt-3{margin-top:calc(var(--spacing)*-3)}.mt-0{margin-top:calc(var(--spacing)*0)}.mt-0\.5{margin-top:calc(var(--spacing)*.5)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-1\.5{margin-top:calc(var(--spacing)*1.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-8{margin-top:calc(var(--spacing)*8)}.-mr-2{margin-right:calc(var(--spacing)*-2)}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-1\.5{margin-right:calc(var(--spacing)*1.5)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mr-4{margin-right:calc(var(--spacing)*4)}.-mb-0\.5{margin-bottom:calc(var(--spacing)*-.5)}.-mb-1\.5{margin-bottom:calc(var(--spacing)*-1.5)}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-0\.5{margin-bottom:calc(var(--spacing)*.5)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-5{margin-bottom:calc(var(--spacing)*5)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.-ml-0\.5{margin-left:calc(var(--spacing)*-.5)}.-ml-1{margin-left:calc(var(--spacing)*-1)}.ml-0{margin-left:calc(var(--spacing)*0)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-5{margin-left:calc(var(--spacing)*5)}.ml-6{margin-left:calc(var(--spacing)*6)}.ml-auto{margin-left:auto}.box-border{box-sizing:border-box}.line-clamp-3{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.block{display:block}.contents{display:contents}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.list-item{display:list-item}.table{display:table}.aspect-square{aspect-ratio:1}.size-3{width:calc(var(--spacing)*3);height:calc(var(--spacing)*3)}.size-3\.5{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5)}.size-4{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.size-5{width:calc(var(--spacing)*5);height:calc(var(--spacing)*5)}.size-\[1em\]{width:1em;height:1em}.h-1{height:calc(var(--spacing)*1)}.h-1\.5{height:calc(var(--spacing)*1.5)}.h-2{height:calc(var(--spacing)*2)}.h-3{height:calc(var(--spacing)*3)}.h-3\.5{height:calc(var(--spacing)*3.5)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-10{height:calc(var(--spacing)*10)}.h-12{height:calc(var(--spacing)*12)}.h-16{height:calc(var(--spacing)*16)}.h-\[1px\]{height:1px}.h-\[5em\]{height:5em}.h-\[5px\]{height:5px}.h-\[8px\]{height:8px}.h-\[25px\]{height:25px}.h-\[40px\]{height:40px}.h-\[80px\]{height:80px}.h-\[640px\]{height:640px}.h-\[var\(--radix-select-trigger-height\)\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-full{height:100%}.h-px{height:1px}.max-h-0{max-height:calc(var(--spacing)*0)}.max-h-60{max-height:calc(var(--spacing)*60)}.max-h-72{max-height:calc(var(--spacing)*72)}.max-h-\[80vh\]{max-height:80vh}.max-h-\[100\%\]{max-height:100%}.max-h-\[160px\]{max-height:160px}.max-h-\[200px\]{max-height:200px}.max-h-\[240px\]{max-height:240px}.max-h-\[300px\]{max-height:300px}.min-h-0{min-height:calc(var(--spacing)*0)}.min-h-\[20px\]{min-height:20px}.min-h-\[24px\]{min-height:24px}.min-h-\[28px\]{min-height:28px}.min-h-\[40px\]{min-height:40px}.min-h-\[60px\]{min-height:60px}.min-h-\[70px\]{min-height:70px}.min-h-\[120px\]{min-height:120px}.min-h-\[150px\]{min-height:150px}.min-h-\[200px\]{min-height:200px}.min-h-screen{min-height:100vh}.w-1\.5{width:calc(var(--spacing)*1.5)}.w-1\/2{width:50%}.w-2{width:calc(var(--spacing)*2)}.w-3{width:calc(var(--spacing)*3)}.w-3\.5{width:calc(var(--spacing)*3.5)}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-6{width:calc(var(--spacing)*6)}.w-7{width:calc(var(--spacing)*7)}.w-8{width:calc(var(--spacing)*8)}.w-9{width:calc(var(--spacing)*9)}.w-10{width:calc(var(--spacing)*10)}.w-12{width:calc(var(--spacing)*12)}.w-16{width:calc(var(--spacing)*16)}.w-20{width:calc(var(--spacing)*20)}.w-24{width:calc(var(--spacing)*24)}.w-28{width:calc(var(--spacing)*28)}.w-48{width:calc(var(--spacing)*48)}.w-72{width:calc(var(--spacing)*72)}.w-\[1px\]{width:1px}.w-\[25px\]{width:25px}.w-\[200px\]{width:200px}.w-\[300px\]{width:300px}.w-\[460px\]{width:460px}.w-\[calc\(100vw-100px\)\]{width:calc(100vw - 100px)}.w-\[var\(--radix-popover-trigger-width\)\]{width:var(--radix-popover-trigger-width)}.w-fit{width:fit-content}.w-full{width:100%}.max-w-96{max-width:calc(var(--spacing)*96)}.max-w-\[80\%\]{max-width:80%}.max-w-\[100px\]{max-width:100px}.max-w-\[250px\]{max-width:250px}.max-w-\[380px\]{max-width:380px}.max-w-\[400px\]{max-width:400px}.max-w-\[calc\(100\%-2rem\)\]{max-width:calc(100% - 2rem)}.max-w-full{max-width:100%}.max-w-md{max-width:var(--container-md)}.max-w-none{max-width:none}.max-w-sm{max-width:var(--container-sm)}.max-w-xs{max-width:var(--container-xs)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-\[8rem\]{min-width:8rem}.min-w-\[20px\]{min-width:20px}.min-w-\[28px\]{min-width:28px}.min-w-\[120px\]{min-width:120px}.min-w-\[280px\]{min-width:280px}.min-w-\[var\(--radix-select-trigger-width\)\]{min-width:var(--radix-select-trigger-width)}.flex-1{flex:1}.flex-none{flex:none}.flex-shrink{flex-shrink:1}.flex-shrink-0{flex-shrink:0}.shrink{flex-shrink:1}.shrink-0{flex-shrink:0}.flex-grow,.flex-grow-1,.grow{flex-grow:1}.translate-x-0{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-4{--tw-translate-x:calc(var(--spacing)*4);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-\[-50\%\]{--tw-translate-x:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-\[-50\%\]{--tw-translate-y:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.scale-75{--tw-scale-x:75%;--tw-scale-y:75%;--tw-scale-z:75%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-80{--tw-scale-x:80%;--tw-scale-y:80%;--tw-scale-z:80%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-90{--tw-scale-x:90%;--tw-scale-y:90%;--tw-scale-z:90%;scale:var(--tw-scale-x)var(--tw-scale-y)}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.animate-pulse{animation:var(--animate-pulse)}.animate-spin{animation:var(--animate-spin)}.cursor-default{cursor:default}.cursor-not-allowed{cursor:not-allowed}.cursor-pointer{cursor:pointer}.cursor-text{cursor:text}.touch-none{touch-action:none}.resize{resize:both}.resize-none{resize:none}.scroll-my-1{scroll-margin-block:calc(var(--spacing)*1)}.list-decimal{list-style-type:decimal}.list-disc{list-style-type:disc}.\[appearance\:textfield\]{-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-\[repeat\(auto-fill\,minmax\(200px\,1fr\)\)\]{grid-template-columns:repeat(auto-fill,minmax(200px,1fr))}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-row-reverse{flex-direction:row-reverse}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.gap-0{gap:calc(var(--spacing)*0)}.gap-0\.5{gap:calc(var(--spacing)*.5)}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-2\.5{gap:calc(var(--spacing)*2.5)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-5{gap:calc(var(--spacing)*5)}.gap-6{gap:calc(var(--spacing)*6)}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.overflow-y-hidden{overflow-y:hidden}.overflow-y-scroll{overflow-y:scroll}.rounded{border-radius:.25rem}.rounded-\[2px\]{border-radius:2px}.rounded-\[3px\]{border-radius:3px}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-sm{border-radius:var(--radius-sm)}.rounded-xs{border-radius:var(--radius-xs)}.rounded-b-md{border-bottom-right-radius:var(--radius-md);border-bottom-left-radius:var(--radius-md)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-l-2{border-left-style:var(--tw-border-style);border-left-width:2px}.border-l-3{border-left-style:var(--tw-border-style);border-left-width:3px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.\!border-vscode-errorForeground{border-color:var(--color-vscode-errorForeground)!important}.border-\[\#333\]{border-color:#333}.border-\[color-mix\(in_srgb\,_var\(--vscode-input-border\)_50\%\,_transparent\)\]{border-color:var(--vscode-input-border)}@supports (color:color-mix(in lab,red,red)){.border-\[color-mix\(in_srgb\,_var\(--vscode-input-border\)_50\%\,_transparent\)\]{border-color:color-mix(in srgb,var(--vscode-input-border)50%,transparent)}}.border-\[var\(--vscode-editorGroup-border\)\]{border-color:var(--vscode-editorGroup-border)}.border-\[var\(--vscode-input-border\,var\(--vscode-input-background\)\)\]{border-color:var(--vscode-input-border,var(--vscode-input-background))}.border-accent{border-color:var(--color-accent)}.border-blue-500\/30{border-color:#3080ff4d}@supports (color:color-mix(in lab,red,red)){.border-blue-500\/30{border-color:color-mix(in oklab,var(--color-blue-500)30%,transparent)}}.border-border,.border-border\/25{border-color:var(--color-border)}@supports (color:color-mix(in lab,red,red)){.border-border\/25{border-color:color-mix(in oklab,var(--color-border)25%,transparent)}}.border-gray-500{border-color:var(--color-gray-500)}.border-neutral-400{border-color:var(--color-neutral-400)}.border-neutral-600\/20{border-color:#52525233}@supports (color:color-mix(in lab,red,red)){.border-neutral-600\/20{border-color:color-mix(in oklab,var(--color-neutral-600)20%,transparent)}}.border-neutral-600\/30{border-color:#5252524d}@supports (color:color-mix(in lab,red,red)){.border-neutral-600\/30{border-color:color-mix(in oklab,var(--color-neutral-600)30%,transparent)}}.border-neutral-600\/50{border-color:#52525280}@supports (color:color-mix(in lab,red,red)){.border-neutral-600\/50{border-color:color-mix(in oklab,var(--color-neutral-600)50%,transparent)}}.border-orange-200{border-color:var(--color-orange-200)}.border-orange-300{border-color:var(--color-orange-300)}.border-primary,.border-primary\/50{border-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.border-primary\/50{border-color:color-mix(in oklab,var(--color-primary)50%,transparent)}}.border-red-200{border-color:var(--color-red-200)}.border-red-500\/30{border-color:#fb2c364d}@supports (color:color-mix(in lab,red,red)){.border-red-500\/30{border-color:color-mix(in oklab,var(--color-red-500)30%,transparent)}}.border-transparent{border-color:#0000}.border-vscode-button-background{border-color:var(--color-vscode-button-background)}.border-vscode-descriptionForeground,.border-vscode-descriptionForeground\/40{border-color:var(--color-vscode-descriptionForeground)}@supports (color:color-mix(in lab,red,red)){.border-vscode-descriptionForeground\/40{border-color:color-mix(in oklab,var(--color-vscode-descriptionForeground)40%,transparent)}}.border-vscode-dropdown-border{border-color:var(--color-vscode-dropdown-border)}.border-vscode-editor-background{border-color:var(--color-vscode-editor-background)}.border-vscode-editorGroup-border{border-color:var(--color-vscode-editorGroup-border)}.border-vscode-focusBorder{border-color:var(--color-vscode-focusBorder)}.border-vscode-foreground{border-color:var(--color-vscode-foreground)}.border-vscode-input-border{border-color:var(--color-vscode-input-border)}.border-vscode-notifications-border{border-color:var(--color-vscode-notifications-border)}.border-vscode-panel-border,.border-vscode-panel-border\/30{border-color:var(--color-vscode-panel-border)}@supports (color:color-mix(in lab,red,red)){.border-vscode-panel-border\/30{border-color:color-mix(in oklab,var(--color-vscode-panel-border)30%,transparent)}}.border-vscode-panel-border\/40{border-color:var(--color-vscode-panel-border)}@supports (color:color-mix(in lab,red,red)){.border-vscode-panel-border\/40{border-color:color-mix(in oklab,var(--color-vscode-panel-border)40%,transparent)}}.border-vscode-sideBar-background{border-color:var(--color-vscode-sideBar-background)}.border-vscode-toolbar-hoverBackground\/30{border-color:var(--color-vscode-toolbar-hoverBackground)}@supports (color:color-mix(in lab,red,red)){.border-vscode-toolbar-hoverBackground\/30{border-color:color-mix(in oklab,var(--color-vscode-toolbar-hoverBackground)30%,transparent)}}.border-vscode-widget-border{border-color:var(--color-vscode-widget-border)}.border-white{border-color:var(--color-white)}.border-white\/20{border-color:#fff3}@supports (color:color-mix(in lab,red,red)){.border-white\/20{border-color:color-mix(in oklab,var(--color-white)20%,transparent)}}.border-yellow-200{border-color:var(--color-yellow-200)}.border-yellow-300{border-color:var(--color-yellow-300)}.border-yellow-500\/30{border-color:#edb2004d}@supports (color:color-mix(in lab,red,red)){.border-yellow-500\/30{border-color:color-mix(in oklab,var(--color-yellow-500)30%,transparent)}}.border-t-transparent{border-top-color:#0000}.border-l-blue-500{border-left-color:var(--color-blue-500)}.bg-\[\#1a1a1a\]{background-color:#1a1a1a}.bg-\[\#2d2d30\]{background-color:#2d2d30}.bg-\[\#4a4a4a\]{background-color:#4a4a4a}.bg-\[\#30402e\]{background-color:#30402e}.bg-\[\#262626\]{background-color:#262626}.bg-\[color-mix\(in_srgb\,var\(--vscode-foreground\)_20\%\,transparent\)\]{background-color:var(--vscode-foreground)}@supports (color:color-mix(in lab,red,red)){.bg-\[color-mix\(in_srgb\,var\(--vscode-foreground\)_20\%\,transparent\)\]{background-color:color-mix(in srgb,var(--vscode-foreground)20%,transparent)}}.bg-\[color-mix\(in_srgb\,var\(--vscode-foreground\)_30\%\,transparent\)\]{background-color:var(--vscode-foreground)}@supports (color:color-mix(in lab,red,red)){.bg-\[color-mix\(in_srgb\,var\(--vscode-foreground\)_30\%\,transparent\)\]{background-color:color-mix(in srgb,var(--vscode-foreground)30%,transparent)}}.bg-\[rgba\(255\,255\,255\,0\.03\)\]{background-color:#ffffff08}.bg-\[var\(--vscode-editor-background\)\]{background-color:var(--vscode-editor-background)}.bg-\[var\(--vscode-foreground\)\]{background-color:var(--vscode-foreground)}.bg-accent{background-color:var(--color-accent)}.bg-black\/50{background-color:#00000080}@supports (color:color-mix(in lab,red,red)){.bg-black\/50{background-color:color-mix(in oklab,var(--color-black)50%,transparent)}}.bg-blue-500{background-color:var(--color-blue-500)}.bg-blue-500\/10{background-color:#3080ff1a}@supports (color:color-mix(in lab,red,red)){.bg-blue-500\/10{background-color:color-mix(in oklab,var(--color-blue-500)10%,transparent)}}.bg-blue-500\/20{background-color:#3080ff33}@supports (color:color-mix(in lab,red,red)){.bg-blue-500\/20{background-color:color-mix(in oklab,var(--color-blue-500)20%,transparent)}}.bg-blue-600{background-color:var(--color-blue-600)}.bg-card{background-color:var(--color-card)}.bg-destructive{background-color:var(--color-destructive)}.bg-gray-100{background-color:var(--color-gray-100)}.bg-gray-200{background-color:var(--color-gray-200)}.bg-gray-300{background-color:var(--color-gray-300)}.bg-gray-400{background-color:var(--color-gray-400)}.bg-gray-500{background-color:var(--color-gray-500)}.bg-green-100{background-color:var(--color-green-100)}.bg-green-500{background-color:var(--color-green-500)}.bg-green-800\/20{background-color:#01663033}@supports (color:color-mix(in lab,red,red)){.bg-green-800\/20{background-color:color-mix(in oklab,var(--color-green-800)20%,transparent)}}.bg-input{background-color:var(--color-input)}.bg-neutral-600\/70{background-color:#525252b3}@supports (color:color-mix(in lab,red,red)){.bg-neutral-600\/70{background-color:color-mix(in oklab,var(--color-neutral-600)70%,transparent)}}.bg-neutral-700{background-color:var(--color-neutral-700)}.bg-neutral-700\/40{background-color:#40404066}@supports (color:color-mix(in lab,red,red)){.bg-neutral-700\/40{background-color:color-mix(in oklab,var(--color-neutral-700)40%,transparent)}}.bg-neutral-700\/60{background-color:#40404099}@supports (color:color-mix(in lab,red,red)){.bg-neutral-700\/60{background-color:color-mix(in oklab,var(--color-neutral-700)60%,transparent)}}.bg-neutral-800\/40{background-color:#26262666}@supports (color:color-mix(in lab,red,red)){.bg-neutral-800\/40{background-color:color-mix(in oklab,var(--color-neutral-800)40%,transparent)}}.bg-neutral-800\/50{background-color:#26262680}@supports (color:color-mix(in lab,red,red)){.bg-neutral-800\/50{background-color:color-mix(in oklab,var(--color-neutral-800)50%,transparent)}}.bg-orange-50{background-color:var(--color-orange-50)}.bg-orange-500{background-color:var(--color-orange-500)}.bg-orange-600{background-color:var(--color-orange-600)}.bg-popover{background-color:var(--color-popover)}.bg-primary{background-color:var(--color-primary)}.bg-red-50{background-color:var(--color-red-50)}.bg-red-500{background-color:var(--color-red-500)}.bg-red-500\/10{background-color:#fb2c361a}@supports (color:color-mix(in lab,red,red)){.bg-red-500\/10{background-color:color-mix(in oklab,var(--color-red-500)10%,transparent)}}.bg-red-500\/20{background-color:#fb2c3633}@supports (color:color-mix(in lab,red,red)){.bg-red-500\/20{background-color:color-mix(in oklab,var(--color-red-500)20%,transparent)}}.bg-red-800\/20{background-color:#9f071233}@supports (color:color-mix(in lab,red,red)){.bg-red-800\/20{background-color:color-mix(in oklab,var(--color-red-800)20%,transparent)}}.bg-secondary{background-color:var(--color-secondary)}.bg-transparent{background-color:#0000}.bg-vscode-badge-background{background-color:var(--color-vscode-badge-background)}.bg-vscode-button-background{background-color:var(--color-vscode-button-background)}.bg-vscode-button-secondaryBackground{background-color:var(--color-vscode-button-secondaryBackground)}.bg-vscode-dropdown-background{background-color:var(--color-vscode-dropdown-background)}.bg-vscode-dropdown-border{background-color:var(--color-vscode-dropdown-border)}.bg-vscode-dropdown-foreground\/10{background-color:var(--color-vscode-dropdown-foreground)}@supports (color:color-mix(in lab,red,red)){.bg-vscode-dropdown-foreground\/10{background-color:color-mix(in oklab,var(--color-vscode-dropdown-foreground)10%,transparent)}}.bg-vscode-editor-background,.bg-vscode-editor-background\/30{background-color:var(--color-vscode-editor-background)}@supports (color:color-mix(in lab,red,red)){.bg-vscode-editor-background\/30{background-color:color-mix(in oklab,var(--color-vscode-editor-background)30%,transparent)}}.bg-vscode-editor-background\/50{background-color:var(--color-vscode-editor-background)}@supports (color:color-mix(in lab,red,red)){.bg-vscode-editor-background\/50{background-color:color-mix(in oklab,var(--color-vscode-editor-background)50%,transparent)}}.bg-vscode-editor-background\/70{background-color:var(--color-vscode-editor-background)}@supports (color:color-mix(in lab,red,red)){.bg-vscode-editor-background\/70{background-color:color-mix(in oklab,var(--color-vscode-editor-background)70%,transparent)}}.bg-vscode-editorWarning-foreground{background-color:var(--color-vscode-editorWarning-foreground)}.bg-vscode-foreground{background-color:var(--color-vscode-foreground)}.bg-vscode-input-background,.bg-vscode-input-background\/50{background-color:var(--color-vscode-input-background)}@supports (color:color-mix(in lab,red,red)){.bg-vscode-input-background\/50{background-color:color-mix(in oklab,var(--color-vscode-input-background)50%,transparent)}}.bg-vscode-input-background\/60{background-color:var(--color-vscode-input-background)}@supports (color:color-mix(in lab,red,red)){.bg-vscode-input-background\/60{background-color:color-mix(in oklab,var(--color-vscode-input-background)60%,transparent)}}.bg-vscode-list-activeSelectionBackground{background-color:var(--color-vscode-list-activeSelectionBackground)}.bg-vscode-notifications-background{background-color:var(--color-vscode-notifications-background)}.bg-vscode-panel-border{background-color:var(--color-vscode-panel-border)}.bg-vscode-sideBar-background{background-color:var(--color-vscode-sideBar-background)}.bg-vscode-textCodeBlock-background{background-color:var(--color-vscode-textCodeBlock-background)}.bg-vscode-toolbar-hoverBackground\/20{background-color:var(--color-vscode-toolbar-hoverBackground)}@supports (color:color-mix(in lab,red,red)){.bg-vscode-toolbar-hoverBackground\/20{background-color:color-mix(in oklab,var(--color-vscode-toolbar-hoverBackground)20%,transparent)}}.bg-white{background-color:var(--color-white)}.bg-yellow-50{background-color:var(--color-yellow-50)}.bg-yellow-100{background-color:var(--color-yellow-100)}.bg-yellow-500{background-color:var(--color-yellow-500)}.bg-yellow-500\/10{background-color:#edb2001a}@supports (color:color-mix(in lab,red,red)){.bg-yellow-500\/10{background-color:color-mix(in oklab,var(--color-yellow-500)10%,transparent)}}.fill-current{fill:currentColor}.object-contain{object-fit:contain}.object-cover{object-fit:cover}.p-0{padding:calc(var(--spacing)*0)}.p-0\.5{padding:calc(var(--spacing)*.5)}.p-1{padding:calc(var(--spacing)*1)}.p-1\.5{padding:calc(var(--spacing)*1.5)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-5{padding:calc(var(--spacing)*5)}.p-6{padding:calc(var(--spacing)*6)}.p-8{padding:calc(var(--spacing)*8)}.p-\[2px\]{padding:2px}.px-0\.5{padding-inline:calc(var(--spacing)*.5)}.px-1{padding-inline:calc(var(--spacing)*1)}.px-1\.5{padding-inline:calc(var(--spacing)*1.5)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-2\.5{padding-inline:calc(var(--spacing)*2.5)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-3\.5{padding-inline:calc(var(--spacing)*3.5)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-6{padding-inline:calc(var(--spacing)*6)}.px-\[15px\]{padding-inline:15px}.py-0\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-2\.5{padding-block:calc(var(--spacing)*2.5)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-6{padding-block:calc(var(--spacing)*6)}.py-\[6px\]{padding-block:6px}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-5{padding-top:calc(var(--spacing)*5)}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-6{padding-right:calc(var(--spacing)*6)}.pr-8{padding-right:calc(var(--spacing)*8)}.pr-12{padding-right:calc(var(--spacing)*12)}.pr-\[6px\]{padding-right:6px}.\!pb-3{padding-bottom:calc(var(--spacing)*3)!important}.pb-1{padding-bottom:calc(var(--spacing)*1)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-5{padding-bottom:calc(var(--spacing)*5)}.pb-6{padding-bottom:calc(var(--spacing)*6)}.pb-\[50px\]{padding-bottom:50px}.pl-2{padding-left:calc(var(--spacing)*2)}.pl-3{padding-left:calc(var(--spacing)*3)}.pl-4{padding-left:calc(var(--spacing)*4)}.pl-5{padding-left:calc(var(--spacing)*5)}.pl-8{padding-left:calc(var(--spacing)*8)}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.font-mono{font-family:var(--font-mono)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\[9\.5px\]{font-size:9.5px}.text-\[10px\]{font-size:10px}.text-\[12px\]{font-size:12px}.text-\[13px\]{font-size:13px}.text-\[14px\]{font-size:14px}.leading-none{--tw-leading:1;line-height:1}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-wide{--tw-tracking:var(--tracking-wide);letter-spacing:var(--tracking-wide)}.tracking-widest{--tw-tracking:var(--tracking-widest);letter-spacing:var(--tracking-widest)}.text-balance{text-wrap:balance}.break-words{overflow-wrap:break-word}.break-all{word-break:break-all}.text-ellipsis{text-overflow:ellipsis}.whitespace-normal{white-space:normal}.whitespace-nowrap{white-space:nowrap}.whitespace-pre-line{white-space:pre-line}.whitespace-pre-wrap{white-space:pre-wrap}.text-blue-400{color:var(--color-blue-400)}.text-blue-500{color:var(--color-blue-500)}.text-blue-600{color:var(--color-blue-600)}.text-current{color:currentColor}.text-destructive{color:var(--color-destructive)}.text-destructive-foreground{color:var(--color-destructive-foreground)}.text-emerald-500{color:var(--color-emerald-500)}.text-foreground{color:var(--color-foreground)}.text-gray-400{color:var(--color-gray-400)}.text-gray-500{color:var(--color-gray-500)}.text-green-400{color:var(--color-green-400)}.text-green-500{color:var(--color-green-500)}.text-green-800{color:var(--color-green-800)}.text-inherit{color:inherit}.text-muted{color:var(--color-muted)}.text-muted-foreground{color:var(--color-muted-foreground)}.text-neutral-200{color:var(--color-neutral-200)}.text-neutral-300{color:var(--color-neutral-300)}.text-neutral-400{color:var(--color-neutral-400)}.text-orange-500{color:var(--color-orange-500)}.text-orange-600{color:var(--color-orange-600)}.text-orange-700{color:var(--color-orange-700)}.text-orange-900{color:var(--color-orange-900)}.text-popover-foreground{color:var(--color-popover-foreground)}.text-primary{color:var(--color-primary)}.text-primary-foreground{color:var(--color-primary-foreground)}.text-purple-500{color:var(--color-purple-500)}.text-red-400{color:var(--color-red-400)}.text-red-500{color:var(--color-red-500)}.text-red-600{color:var(--color-red-600)}.text-red-700{color:var(--color-red-700)}.text-secondary-foreground{color:var(--color-secondary-foreground)}.text-transparent{color:#0000}.text-vscode-button-foreground{color:var(--color-vscode-button-foreground)}.text-vscode-button-secondaryForeground{color:var(--color-vscode-button-secondaryForeground)}.text-vscode-charts-green{color:var(--color-vscode-charts-green)}.text-vscode-descriptionForeground{color:var(--color-vscode-descriptionForeground)}.text-vscode-dropdown-foreground{color:var(--color-vscode-dropdown-foreground)}.text-vscode-editor-background{color:var(--color-vscode-editor-background)}.text-vscode-editor-foreground{color:var(--color-vscode-editor-foreground)}.text-vscode-editorWarning-foreground{color:var(--color-vscode-editorWarning-foreground)}.text-vscode-errorForeground{color:var(--color-vscode-errorForeground)}.text-vscode-foreground,.text-vscode-foreground\/70{color:var(--color-vscode-foreground)}@supports (color:color-mix(in lab,red,red)){.text-vscode-foreground\/70{color:color-mix(in oklab,var(--color-vscode-foreground)70%,transparent)}}.text-vscode-foreground\/80{color:var(--color-vscode-foreground)}@supports (color:color-mix(in lab,red,red)){.text-vscode-foreground\/80{color:color-mix(in oklab,var(--color-vscode-foreground)80%,transparent)}}.text-vscode-input-foreground{color:var(--color-vscode-input-foreground)}.text-vscode-list-activeSelectionForeground{color:var(--color-vscode-list-activeSelectionForeground)}.text-vscode-notifications-foreground{color:var(--color-vscode-notifications-foreground)}.text-vscode-sideBar-foreground{color:var(--color-vscode-sideBar-foreground)}.text-vscode-textLink-foreground{color:var(--color-vscode-textLink-foreground)}.text-white{color:var(--color-white)}.text-yellow-400{color:var(--color-yellow-400)}.text-yellow-500{color:var(--color-yellow-500)}.text-yellow-600{color:var(--color-yellow-600)}.text-yellow-700{color:var(--color-yellow-700)}.text-yellow-800{color:var(--color-yellow-800)}.text-yellow-900{color:var(--color-yellow-900)}.capitalize{text-transform:capitalize}.lowercase{text-transform:lowercase}.uppercase{text-transform:uppercase}.italic{font-style:italic}.no-underline{text-decoration-line:none}.underline{text-decoration-line:underline}.underline-offset-4{text-underline-offset:4px}.opacity-0{opacity:0}.opacity-25{opacity:.25}.opacity-40{opacity:.4}.opacity-50{opacity:.5}.opacity-60{opacity:.6}.opacity-70{opacity:.7}.opacity-80{opacity:.8}.opacity-85{opacity:.85}.opacity-90{opacity:.9}.opacity-100{opacity:1}.shadow-inner{--tw-shadow:inset 0 2px 4px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-0{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-1{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-gray-500\/50{--tw-shadow-color:#6a728280}@supports (color:color-mix(in lab,red,red)){.shadow-gray-500\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-gray-500)50%,transparent)var(--tw-shadow-alpha),transparent)}}.shadow-green-500\/50{--tw-shadow-color:#00c75880}@supports (color:color-mix(in lab,red,red)){.shadow-green-500\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-green-500)50%,transparent)var(--tw-shadow-alpha),transparent)}}.shadow-red-500\/50{--tw-shadow-color:#fb2c3680}@supports (color:color-mix(in lab,red,red)){.shadow-red-500\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-red-500)50%,transparent)var(--tw-shadow-alpha),transparent)}}.shadow-yellow-500\/50{--tw-shadow-color:#edb20080}@supports (color:color-mix(in lab,red,red)){.shadow-yellow-500\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-yellow-500)50%,transparent)var(--tw-shadow-alpha),transparent)}}.ring-blue-400{--tw-ring-color:var(--color-blue-400)}.ring-offset-background{--tw-ring-offset-color:var(--color-background)}.outline-hidden{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.outline-hidden{outline-offset:2px;outline:2px solid #0000}}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.blur{--tw-blur:blur(8px);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.brightness-90{--tw-brightness:brightness(90%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.drop-shadow-md{--tw-drop-shadow-size:drop-shadow(0 3px 3px var(--tw-drop-shadow-color,#0000001f));--tw-drop-shadow:drop-shadow(var(--drop-shadow-md));filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.grayscale-\[30\%\]{--tw-grayscale:grayscale(30%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.backdrop-blur-sm{--tw-backdrop-blur:blur(var(--blur-sm));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\[color\,box-shadow\]{transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.duration-150{--tw-duration:.15s;transition-duration:.15s}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.duration-1000{--tw-duration:1s;transition-duration:1s}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.forced-color-adjust-none{forced-color-adjust:none}.animate-in{--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial;animation-name:enter;animation-duration:.15s}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;user-select:none}.duration-150{animation-duration:.15s}.duration-200{animation-duration:.2s}.duration-300{animation-duration:.3s}.duration-1000{animation-duration:1s}.ease-in-out{animation-timing-function:cubic-bezier(.4,0,.2,1)}.ease-out{animation-timing-function:cubic-bezier(0,0,.2,1)}.fade-in-0{--tw-enter-opacity:0}.running{animation-play-state:running}.zoom-in-95{--tw-enter-scale:.95}@media (hover:hover){.group-hover\:flex:is(:where(.group):hover *){display:flex}.group-hover\:hidden:is(:where(.group):hover *){display:none}.group-hover\:text-foreground:is(:where(.group):hover *){color:var(--color-foreground)}.group-hover\:opacity-25:is(:where(.group):hover *){opacity:.25}.group-hover\:opacity-70:is(:where(.group):hover *){opacity:.7}.group-hover\:opacity-100:is(:where(.group):hover *){opacity:1}}.file\:border-0::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file\:bg-transparent::file-selector-button{background-color:#0000}.file\:text-sm::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file\:font-medium::file-selector-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\:text-foreground::file-selector-button{color:var(--color-foreground)}.placeholder\:text-muted-foreground::placeholder{color:var(--color-muted-foreground)}.placeholder\:text-neutral-400::placeholder{color:var(--color-neutral-400)}.last\:mb-0:last-child{margin-bottom:calc(var(--spacing)*0)}.last\:border-r-0:last-child{border-right-style:var(--tw-border-style);border-right-width:0}.last\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.focus-within\:border-0:focus-within{border-style:var(--tw-border-style);border-width:0}.focus-within\:border-neutral-600\/30:focus-within{border-color:#5252524d}@supports (color:color-mix(in lab,red,red)){.focus-within\:border-neutral-600\/30:focus-within{border-color:color-mix(in oklab,var(--color-neutral-600)30%,transparent)}}.focus-within\:outline-none:focus-within{--tw-outline-style:none;outline-style:none}@media (hover:hover){.hover\:border-\[rgba\(255\,255\,255\,0\.08\)\]:hover{border-color:#ffffff14}.hover\:border-\[rgba\(255\,255\,255\,0\.15\)\]:hover{border-color:#ffffff26}.hover\:border-vscode-toolbar-hoverBackground\/60:hover{border-color:var(--color-vscode-toolbar-hoverBackground)}@supports (color:color-mix(in lab,red,red)){.hover\:border-vscode-toolbar-hoverBackground\/60:hover{border-color:color-mix(in oklab,var(--color-vscode-toolbar-hoverBackground)60%,transparent)}}.hover\:bg-\[\#3e3e42\]:hover{background-color:#3e3e42}.hover\:bg-\[\#303030\]:hover{background-color:#303030}.hover\:bg-\[\#395136\]:hover{background-color:#395136}.hover\:bg-\[rgba\(255\,255\,255\,0\.03\)\]:hover{background-color:#ffffff08}.hover\:bg-\[rgba\(255\,255\,255\,0\.05\)\]:hover{background-color:#ffffff0d}.hover\:bg-accent:hover{background-color:var(--color-accent)}.hover\:bg-black\/20:hover{background-color:#0003}@supports (color:color-mix(in lab,red,red)){.hover\:bg-black\/20:hover{background-color:color-mix(in oklab,var(--color-black)20%,transparent)}}.hover\:bg-blue-700:hover{background-color:var(--color-blue-700)}.hover\:bg-destructive\/80:hover{background-color:var(--color-destructive)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-destructive\/80:hover{background-color:color-mix(in oklab,var(--color-destructive)80%,transparent)}}.hover\:bg-destructive\/90:hover{background-color:var(--color-destructive)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-destructive\/90:hover{background-color:color-mix(in oklab,var(--color-destructive)90%,transparent)}}.hover\:bg-neutral-600:hover{background-color:var(--color-neutral-600)}.hover\:bg-neutral-600\/20:hover{background-color:#52525233}@supports (color:color-mix(in lab,red,red)){.hover\:bg-neutral-600\/20:hover{background-color:color-mix(in oklab,var(--color-neutral-600)20%,transparent)}}.hover\:bg-neutral-600\/30:hover{background-color:#5252524d}@supports (color:color-mix(in lab,red,red)){.hover\:bg-neutral-600\/30:hover{background-color:color-mix(in oklab,var(--color-neutral-600)30%,transparent)}}.hover\:bg-neutral-600\/40:hover{background-color:#52525266}@supports (color:color-mix(in lab,red,red)){.hover\:bg-neutral-600\/40:hover{background-color:color-mix(in oklab,var(--color-neutral-600)40%,transparent)}}.hover\:bg-neutral-600\/60:hover{background-color:#52525299}@supports (color:color-mix(in lab,red,red)){.hover\:bg-neutral-600\/60:hover{background-color:color-mix(in oklab,var(--color-neutral-600)60%,transparent)}}.hover\:bg-orange-700:hover{background-color:var(--color-orange-700)}.hover\:bg-primary\/80:hover{background-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-primary\/80:hover{background-color:color-mix(in oklab,var(--color-primary)80%,transparent)}}.hover\:bg-primary\/90:hover{background-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--color-primary)90%,transparent)}}.hover\:bg-secondary:hover,.hover\:bg-secondary\/80:hover{background-color:var(--color-secondary)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-secondary\/80:hover{background-color:color-mix(in oklab,var(--color-secondary)80%,transparent)}}.hover\:bg-transparent:hover{background-color:#0000}.hover\:bg-vscode-button-hoverBackground:hover{background-color:var(--color-vscode-button-hoverBackground)}.hover\:bg-vscode-list-hoverBackground:hover{background-color:var(--color-vscode-list-hoverBackground)}.hover\:bg-vscode-toolbar-hoverBackground:hover,.hover\:bg-vscode-toolbar-hoverBackground\/30:hover{background-color:var(--color-vscode-toolbar-hoverBackground)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-vscode-toolbar-hoverBackground\/30:hover{background-color:color-mix(in oklab,var(--color-vscode-toolbar-hoverBackground)30%,transparent)}}.hover\:bg-vscode-toolbar-hoverBackground\/40:hover{background-color:var(--color-vscode-toolbar-hoverBackground)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-vscode-toolbar-hoverBackground\/40:hover{background-color:color-mix(in oklab,var(--color-vscode-toolbar-hoverBackground)40%,transparent)}}.hover\:bg-white\/10:hover{background-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){.hover\:bg-white\/10:hover{background-color:color-mix(in oklab,var(--color-white)10%,transparent)}}.hover\:\!text-green-300:hover{color:var(--color-green-300)!important}.hover\:\!text-neutral-200:hover{color:var(--color-neutral-200)!important}.hover\:\!text-red-300:hover{color:var(--color-red-300)!important}.hover\:text-accent-foreground:hover{color:var(--color-accent-foreground)}.hover\:text-blue-700:hover{color:var(--color-blue-700)}.hover\:text-foreground:hover{color:var(--color-foreground)}.hover\:text-neutral-100:hover{color:var(--color-neutral-100)}.hover\:text-neutral-200:hover{color:var(--color-neutral-200)}.hover\:text-red-400:hover{color:var(--color-red-400)}.hover\:text-red-700:hover{color:var(--color-red-700)}.hover\:text-vscode-foreground:hover{color:var(--color-vscode-foreground)}.hover\:text-vscode-textLink-foreground:hover{color:var(--color-vscode-textLink-foreground)}.hover\:text-white:hover{color:var(--color-white)}.hover\:underline:hover{text-decoration-line:underline}.hover\:opacity-100:hover{opacity:1}}.focus\:border-0:focus{border-style:var(--tw-border-style);border-width:0}.focus\:border-vscode-focusBorder:focus{border-color:var(--color-vscode-focusBorder)}.focus\:bg-vscode-list-activeSelectionBackground:focus{background-color:var(--color-vscode-list-activeSelectionBackground)}.focus\:text-accent-foreground:focus{color:var(--color-accent-foreground)}.focus\:text-vscode-list-activeSelectionForeground:focus{color:var(--color-vscode-list-activeSelectionForeground)}.focus\:shadow-none:focus{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-0:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-ring:focus{--tw-ring-color:var(--color-ring)}.focus\:ring-vscode-focusBorder:focus{--tw-ring-color:var(--color-vscode-focusBorder)}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.focus\:outline-0:focus{outline-style:var(--tw-outline-style);outline-width:0}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}.focus-visible\:border-vscode-focusBorder:focus-visible{border-color:var(--color-vscode-focusBorder)}.focus-visible\:shadow-none:focus-visible{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-1:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-2:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-blue-500:focus-visible{--tw-ring-color:var(--color-blue-500)}.focus-visible\:ring-ring:focus-visible{--tw-ring-color:var(--color-ring)}.focus-visible\:ring-vscode-focusBorder:focus-visible{--tw-ring-color:var(--color-vscode-focusBorder)}.focus-visible\:ring-offset-2:focus-visible{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\:outline-none:focus-visible{--tw-outline-style:none;outline-style:none}.focus-visible\:ring-inset:focus-visible{--tw-ring-inset:inset}.active\:bg-\[\#1e1e1e\]:active{background-color:#1e1e1e}.active\:bg-\[rgba\(255\,255\,255\,0\.1\)\]:active{background-color:#ffffff1a}.active\:bg-neutral-600\/50:active{background-color:#52525280}@supports (color:color-mix(in lab,red,red)){.active\:bg-neutral-600\/50:active{background-color:color-mix(in oklab,var(--color-neutral-600)50%,transparent)}}.active\:bg-neutral-700:active{background-color:var(--color-neutral-700)}.active\:bg-transparent:active{background-color:#0000}.active\:opacity-80:active{opacity:.8}.active\:opacity-90:active{opacity:.9}.disabled\:pointer-events-none:disabled{pointer-events:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}.aria-expanded\:border-vscode-focusBorder[aria-expanded=true]{border-color:var(--color-vscode-focusBorder)}.aria-invalid\:border-destructive[aria-invalid=true]{border-color:var(--color-destructive)}.data-\[compact\=true\]\:w-12[data-compact=true]{width:calc(var(--spacing)*12)}.data-\[compact\=true\]\:p-4[data-compact=true]{padding:calc(var(--spacing)*4)}.data-\[disabled\]\:pointer-events-none[data-disabled]{pointer-events:none}.data-\[disabled\]\:opacity-50[data-disabled]{opacity:.5}.data-\[disabled\=true\]\:pointer-events-none[data-disabled=true]{pointer-events:none}.data-\[disabled\=true\]\:opacity-50[data-disabled=true]{opacity:.5}.data-\[placeholder\]\:text-muted-foreground[data-placeholder]{color:var(--color-muted-foreground)}.data-\[selected\=true\]\:bg-vscode-list-activeSelectionBackground[data-selected=true]{background-color:var(--color-vscode-list-activeSelectionBackground)}.data-\[selected\=true\]\:text-vscode-list-activeSelectionForeground[data-selected=true]{color:var(--color-vscode-list-activeSelectionForeground)}.data-\[side\=bottom\]\:translate-y-1[data-side=bottom]{--tw-translate-y:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=bottom\]\:slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y:-.5rem}.data-\[side\=left\]\:-translate-x-1[data-side=left]{--tw-translate-x:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=left\]\:slide-in-from-right-2[data-side=left]{--tw-enter-translate-x:.5rem}.data-\[side\=right\]\:translate-x-1[data-side=right]{--tw-translate-x:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=right\]\:slide-in-from-left-2[data-side=right]{--tw-enter-translate-x:-.5rem}.data-\[side\=top\]\:-translate-y-1[data-side=top]{--tw-translate-y:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=top\]\:slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y:.5rem}:is(.\*\:data-\[slot\=select-value\]\:line-clamp-1>*)[data-slot=select-value]{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}:is(.\*\:data-\[slot\=select-value\]\:flex>*)[data-slot=select-value]{display:flex}:is(.\*\:data-\[slot\=select-value\]\:items-center>*)[data-slot=select-value]{align-items:center}:is(.\*\:data-\[slot\=select-value\]\:gap-2>*)[data-slot=select-value]{gap:calc(var(--spacing)*2)}.data-\[state\=checked\]\:bg-vscode-descriptionForeground[data-state=checked]{background-color:var(--color-vscode-descriptionForeground)}.data-\[state\=checked\]\:bg-vscode-foreground[data-state=checked]{background-color:var(--color-vscode-foreground)}.data-\[state\=checked\]\:text-primary-foreground[data-state=checked]{color:var(--color-primary-foreground)}.data-\[state\=checked\]\:text-white[data-state=checked]{color:var(--color-white)}.data-\[state\=closed\]\:animate-out[data-state=closed]{--tw-exit-opacity:initial;--tw-exit-scale:initial;--tw-exit-rotate:initial;--tw-exit-translate-x:initial;--tw-exit-translate-y:initial;animation-name:exit;animation-duration:.15s}.data-\[state\=closed\]\:fade-out-0[data-state=closed]{--tw-exit-opacity:0}.data-\[state\=closed\]\:zoom-out-95[data-state=closed]{--tw-exit-scale:.95}.data-\[state\=open\]\:bg-accent[data-state=open]{background-color:var(--color-accent)}.data-\[state\=open\]\:text-muted-foreground[data-state=open]{color:var(--color-muted-foreground)}.data-\[state\=open\]\:animate-in[data-state=open]{--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial;animation-name:enter;animation-duration:.15s}.data-\[state\=open\]\:fade-in-0[data-state=open]{--tw-enter-opacity:0}.data-\[state\=open\]\:zoom-in-95[data-state=open]{--tw-enter-scale:.95}@media (min-width:370px){.min-\[370px\]\:px-10{padding-inline:calc(var(--spacing)*10)}}@media (min-width:40rem){.sm\:max-w-\[500px\]{max-width:500px}.sm\:max-w-\[600px\]{max-width:600px}.sm\:max-w-lg{max-width:var(--container-lg)}.sm\:max-w-md{max-width:var(--container-md)}.sm\:flex-row{flex-direction:row}.sm\:justify-end{justify-content:flex-end}.sm\:text-left{text-align:left}}@media (min-width:48rem){.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (prefers-color-scheme:dark){.dark\:border-red-800{border-color:var(--color-red-800)}.dark\:bg-gray-800{background-color:var(--color-gray-800)}.dark\:bg-green-900{background-color:var(--color-green-900)}.dark\:bg-red-900\/20{background-color:#82181a33}@supports (color:color-mix(in lab,red,red)){.dark\:bg-red-900\/20{background-color:color-mix(in oklab,var(--color-red-900)20%,transparent)}}.dark\:bg-yellow-900{background-color:var(--color-yellow-900)}.dark\:text-blue-400{color:var(--color-blue-400)}.dark\:text-green-200{color:var(--color-green-200)}.dark\:text-red-300{color:var(--color-red-300)}.dark\:text-red-400{color:var(--color-red-400)}.dark\:text-yellow-200{color:var(--color-yellow-200)}}.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{padding-inline:calc(var(--spacing)*2)}.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading]{padding-block:calc(var(--spacing)*1.5)}.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading]{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading]{color:var(--color-muted-foreground)}.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group]{padding-inline:calc(var(--spacing)*2)}.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden])~[cmdk-group]{padding-top:calc(var(--spacing)*0)}.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg{height:calc(var(--spacing)*5)}.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg{width:calc(var(--spacing)*5)}.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input]{height:calc(var(--spacing)*12)}.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item]{padding-inline:calc(var(--spacing)*2)}.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item]{padding-block:calc(var(--spacing)*3)}.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg{height:calc(var(--spacing)*5)}.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg{width:calc(var(--spacing)*5)}.\[\&_svg\]\:pointer-events-none svg{pointer-events:none}.\[\&_svg\]\:size-4 svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\[\&_svg\]\:shrink-0 svg{flex-shrink:0}.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*=size-]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*=text-]){color:var(--color-muted-foreground)}.\[\&\.narrow_\.tab-label\]\:hidden.narrow .tab-label{display:none}.\[\&\:\:-webkit-inner-spin-button\]\:appearance-none::-webkit-inner-spin-button{-webkit-appearance:none;-moz-appearance:none;appearance:none}.\[\&\:\:-webkit-outer-spin-button\]\:appearance-none::-webkit-outer-spin-button{-webkit-appearance:none;-moz-appearance:none;appearance:none}:is(.\*\:\[span\]\:last\:flex>*):is(span):last-child{display:flex}:is(.\*\:\[span\]\:last\:items-center>*):is(span):last-child{align-items:center}:is(.\*\:\[span\]\:last\:gap-2>*):is(span):last-child{gap:calc(var(--spacing)*2)}.\[\&\>li\]\:mb-1>li{margin-bottom:calc(var(--spacing)*1)}.\[\&\>li\:last-child\]\:mb-0>li:last-child{margin-bottom:calc(var(--spacing)*0)}.\[\&\>li\>ol\]\:mt-1>li>ol,.\[\&\>li\>ul\]\:mt-1>li>ul{margin-top:calc(var(--spacing)*1)}.\[\&\>svg\]\:size-4>svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\[\&\>svg\]\:shrink-0>svg{flex-shrink:0}@media (min-width:600px){.\[\@media\(min-width\:600px\)\]\:gap-4{gap:calc(var(--spacing)*4)}}@media (min-width:800px){.\[\@media\(min-width\:800px\)\]\:max-w-\[800px\]{max-width:800px}}}textarea:focus,textarea:focus-visible,textarea:focus-within,.chat-text-area textarea:focus,.chat-text-area textarea:focus-visible,.chat-text-area textarea:focus-within,.chat-text-area :focus,.chat-text-area :focus-visible,.chat-text-area :focus-within{box-shadow:none!important;border:none!important;outline:none!important}.focus\:outline-0{outline:0!important}html{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;height:100%}body{margin:0;line-height:1.25}body.scrollable,.scrollable,body.code-block-scrollable,.code-block-scrollable{border-color:#0000;transition:border-color .7s linear}body:hover.scrollable,body:hover .scrollable,body:focus-within.scrollable,body:focus-within .scrollable,body:hover.code-block-scrollable,body:hover .code-block-scrollable,body:focus-within.code-block-scrollable,body:focus-within .code-block-scrollable{border-color:var(--vscode-scrollbarSlider-background);transition:none}.scrollable::-webkit-scrollbar-corner{background-color:#0000!important}.scrollable::-webkit-scrollbar-thumb{border-color:inherit;background-color:#0000;border-right-style:inset;border-right-width:calc(100vw + 100vh);border-radius:unset!important}.scrollable::-webkit-scrollbar-thumb:hover{border-color:var(--vscode-scrollbarSlider-hoverBackground)}.scrollable::-webkit-scrollbar-thumb:active{border-color:var(--vscode-scrollbarSlider-activeBackground)}@supports selector(::-webkit-scrollbar){html{scrollbar-color:unset}}.code-block-scrollable::-webkit-scrollbar-track{background:0 0}.code-block-scrollable::-webkit-scrollbar-thumb{background-color:var(--vscode-scrollbarSlider-background);background-clip:content-box;border:2px solid #0000;border-radius:5px}.code-block-scrollable::-webkit-scrollbar-thumb:hover{background-color:var(--vscode-scrollbarSlider-hoverBackground)}.code-block-scrollable::-webkit-scrollbar-thumb:active{background-color:var(--vscode-scrollbarSlider-activeBackground)}.code-block-scrollable::-webkit-scrollbar-corner{background-color:#0000}.scrollbar-hide{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide::-webkit-scrollbar{display:none}.dropdown-container{box-sizing:border-box;flex-flow:column;justify-content:flex-start;align-items:flex-start;display:flex}.dropdown-container label{color:var(--vscode-foreground);cursor:pointer;font-size:var(--vscode-font-size);margin-bottom:2px;line-height:normal;display:block}#api-provider>div>ul{overflow:unset}vscode-dropdown::part(listbox){border-color:var(--vscode-scrollbarSlider-background);scrollbar-color:var(--vscode-scrollbarSlider-background)transparent;transition:none}.input-icon-button{cursor:pointer;opacity:.65}.input-icon-button:hover{opacity:1}.input-icon-button.disabled{cursor:not-allowed;opacity:.4}.input-icon-button.disabled:hover{opacity:.4}mark.mention-context-textarea-highlight{background-color:var(--vscode-input-background)!important;color:var(--vscode-editor-foreground)!important;border:1px solid var(--vscode-input-border)!important;box-shadow:none!important;font-size:inherit!important;line-height:inherit!important;vertical-align:middle!important;white-space:nowrap!important;text-overflow:ellipsis!important;z-index:10!important;pointer-events:none!important;border-radius:0!important;margin:0 1px!important;padding:1px 3px!important;display:inline-block!important;position:relative!important;overflow:visible!important}@supports (color:color-mix(in lab,red,red)){mark.mention-context-textarea-highlight{background-color:color-mix(in srgb,var(--vscode-input-background)80%,var(--vscode-editor-background)20%)!important}}.text-transparent mark.mention-context-textarea-highlight{color:var(--vscode-editor-foreground)!important;background-color:var(--vscode-input-background)!important}@supports (color:color-mix(in lab,red,red)){.text-transparent mark.mention-context-textarea-highlight{background-color:color-mix(in srgb,var(--vscode-input-background)80%,var(--vscode-editor-background)20%)!important}}.mention-context-highlight,.mention-context-highlight-with-shadow{border:1px solid var(--vscode-input-border);border-radius:0;padding:1px 3px}.vscrui-checkbox__listbox>ul{max-height:unset!important}.vscrui-checkbox svg{min-width:16px;min-height:16px}input[cmdk-input]:focus{outline:none}.custom-markdown>pre{background-color:#0000!important}.codicon[class*=codicon-]{text-rendering:geometricPrecision!important}a:focus{outline:1px solid var(--vscode-focusBorder)}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}@font-face{font-family:codicon;font-display:block;src:url(/assets/codicon.ttf?38dcd33a732ebca5a557e04831e9e235) format("truetype")}.codicon[class*=codicon-]{font: 16px/1 codicon;display:inline-block;text-decoration:none;text-rendering:auto;text-align:center;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;user-select:none;-webkit-user-select:none;-ms-user-select:none}@keyframes codicon-spin{to{transform:rotate(360deg)}}.codicon-sync.codicon-modifier-spin,.codicon-loading.codicon-modifier-spin,.codicon-gear.codicon-modifier-spin{animation:codicon-spin 1.5s steps(30) infinite}.codicon-modifier-disabled{opacity:.5}.codicon-modifier-hidden{opacity:0}.codicon-loading{animation-duration:1s!important;animation-timing-function:cubic-bezier(.53,.21,.29,.67)!important}.codicon-add:before{content:""}.codicon-plus:before{content:""}.codicon-gist-new:before{content:""}.codicon-repo-create:before{content:""}.codicon-lightbulb:before{content:""}.codicon-light-bulb:before{content:""}.codicon-repo:before{content:""}.codicon-repo-delete:before{content:""}.codicon-gist-fork:before{content:""}.codicon-repo-forked:before{content:""}.codicon-git-pull-request:before{content:""}.codicon-git-pull-request-abandoned:before{content:""}.codicon-record-keys:before{content:""}.codicon-keyboard:before{content:""}.codicon-tag:before{content:""}.codicon-git-pull-request-label:before{content:""}.codicon-tag-add:before{content:""}.codicon-tag-remove:before{content:""}.codicon-person:before{content:""}.codicon-person-follow:before{content:""}.codicon-person-outline:before{content:""}.codicon-person-filled:before{content:""}.codicon-git-branch:before{content:""}.codicon-git-branch-create:before{content:""}.codicon-git-branch-delete:before{content:""}.codicon-source-control:before{content:""}.codicon-mirror:before{content:""}.codicon-mirror-public:before{content:""}.codicon-star:before{content:""}.codicon-star-add:before{content:""}.codicon-star-delete:before{content:""}.codicon-star-empty:before{content:""}.codicon-comment:before{content:""}.codicon-comment-add:before{content:""}.codicon-alert:before{content:""}.codicon-warning:before{content:""}.codicon-search:before{content:""}.codicon-search-save:before{content:""}.codicon-log-out:before{content:""}.codicon-sign-out:before{content:""}.codicon-log-in:before{content:""}.codicon-sign-in:before{content:""}.codicon-eye:before{content:""}.codicon-eye-unwatch:before{content:""}.codicon-eye-watch:before{content:""}.codicon-circle-filled:before{content:""}.codicon-primitive-dot:before{content:""}.codicon-close-dirty:before{content:""}.codicon-debug-breakpoint:before{content:""}.codicon-debug-breakpoint-disabled:before{content:""}.codicon-debug-hint:before{content:""}.codicon-terminal-decoration-success:before{content:""}.codicon-primitive-square:before{content:""}.codicon-edit:before{content:""}.codicon-pencil:before{content:""}.codicon-info:before{content:""}.codicon-issue-opened:before{content:""}.codicon-gist-private:before{content:""}.codicon-git-fork-private:before{content:""}.codicon-lock:before{content:""}.codicon-mirror-private:before{content:""}.codicon-close:before{content:""}.codicon-remove-close:before{content:""}.codicon-x:before{content:""}.codicon-repo-sync:before{content:""}.codicon-sync:before{content:""}.codicon-clone:before{content:""}.codicon-desktop-download:before{content:""}.codicon-beaker:before{content:""}.codicon-microscope:before{content:""}.codicon-vm:before{content:""}.codicon-device-desktop:before{content:""}.codicon-file:before{content:""}.codicon-file-text:before{content:""}.codicon-more:before{content:""}.codicon-ellipsis:before{content:""}.codicon-kebab-horizontal:before{content:""}.codicon-mail-reply:before{content:""}.codicon-reply:before{content:""}.codicon-organization:before{content:""}.codicon-organization-filled:before{content:""}.codicon-organization-outline:before{content:""}.codicon-new-file:before{content:""}.codicon-file-add:before{content:""}.codicon-new-folder:before{content:""}.codicon-file-directory-create:before{content:""}.codicon-trash:before{content:""}.codicon-trashcan:before{content:""}.codicon-history:before{content:""}.codicon-clock:before{content:""}.codicon-folder:before{content:""}.codicon-file-directory:before{content:""}.codicon-symbol-folder:before{content:""}.codicon-logo-github:before{content:""}.codicon-mark-github:before{content:""}.codicon-github:before{content:""}.codicon-terminal:before{content:""}.codicon-console:before{content:""}.codicon-repl:before{content:""}.codicon-zap:before{content:""}.codicon-symbol-event:before{content:""}.codicon-error:before{content:""}.codicon-stop:before{content:""}.codicon-variable:before{content:""}.codicon-symbol-variable:before{content:""}.codicon-array:before{content:""}.codicon-symbol-array:before{content:""}.codicon-symbol-module:before{content:""}.codicon-symbol-package:before{content:""}.codicon-symbol-namespace:before{content:""}.codicon-symbol-object:before{content:""}.codicon-symbol-method:before{content:""}.codicon-symbol-function:before{content:""}.codicon-symbol-constructor:before{content:""}.codicon-symbol-boolean:before{content:""}.codicon-symbol-null:before{content:""}.codicon-symbol-numeric:before{content:""}.codicon-symbol-number:before{content:""}.codicon-symbol-structure:before{content:""}.codicon-symbol-struct:before{content:""}.codicon-symbol-parameter:before{content:""}.codicon-symbol-type-parameter:before{content:""}.codicon-symbol-key:before{content:""}.codicon-symbol-text:before{content:""}.codicon-symbol-reference:before{content:""}.codicon-go-to-file:before{content:""}.codicon-symbol-enum:before{content:""}.codicon-symbol-value:before{content:""}.codicon-symbol-ruler:before{content:""}.codicon-symbol-unit:before{content:""}.codicon-activate-breakpoints:before{content:""}.codicon-archive:before{content:""}.codicon-arrow-both:before{content:""}.codicon-arrow-down:before{content:""}.codicon-arrow-left:before{content:""}.codicon-arrow-right:before{content:""}.codicon-arrow-small-down:before{content:""}.codicon-arrow-small-left:before{content:""}.codicon-arrow-small-right:before{content:""}.codicon-arrow-small-up:before{content:""}.codicon-arrow-up:before{content:""}.codicon-bell:before{content:""}.codicon-bold:before{content:""}.codicon-book:before{content:""}.codicon-bookmark:before{content:""}.codicon-debug-breakpoint-conditional-unverified:before{content:""}.codicon-debug-breakpoint-conditional:before{content:""}.codicon-debug-breakpoint-conditional-disabled:before{content:""}.codicon-debug-breakpoint-data-unverified:before{content:""}.codicon-debug-breakpoint-data:before{content:""}.codicon-debug-breakpoint-data-disabled:before{content:""}.codicon-debug-breakpoint-log-unverified:before{content:""}.codicon-debug-breakpoint-log:before{content:""}.codicon-debug-breakpoint-log-disabled:before{content:""}.codicon-briefcase:before{content:""}.codicon-broadcast:before{content:""}.codicon-browser:before{content:""}.codicon-bug:before{content:""}.codicon-calendar:before{content:""}.codicon-case-sensitive:before{content:""}.codicon-check:before{content:""}.codicon-checklist:before{content:""}.codicon-chevron-down:before{content:""}.codicon-chevron-left:before{content:""}.codicon-chevron-right:before{content:""}.codicon-chevron-up:before{content:""}.codicon-chrome-close:before{content:""}.codicon-chrome-maximize:before{content:""}.codicon-chrome-minimize:before{content:""}.codicon-chrome-restore:before{content:""}.codicon-circle-outline:before{content:""}.codicon-circle:before{content:""}.codicon-debug-breakpoint-unverified:before{content:""}.codicon-terminal-decoration-incomplete:before{content:""}.codicon-circle-slash:before{content:""}.codicon-circuit-board:before{content:""}.codicon-clear-all:before{content:""}.codicon-clippy:before{content:""}.codicon-close-all:before{content:""}.codicon-cloud-download:before{content:""}.codicon-cloud-upload:before{content:""}.codicon-code:before{content:""}.codicon-collapse-all:before{content:""}.codicon-color-mode:before{content:""}.codicon-comment-discussion:before{content:""}.codicon-credit-card:before{content:""}.codicon-dash:before{content:""}.codicon-dashboard:before{content:""}.codicon-database:before{content:""}.codicon-debug-continue:before{content:""}.codicon-debug-disconnect:before{content:""}.codicon-debug-pause:before{content:""}.codicon-debug-restart:before{content:""}.codicon-debug-start:before{content:""}.codicon-debug-step-into:before{content:""}.codicon-debug-step-out:before{content:""}.codicon-debug-step-over:before{content:""}.codicon-debug-stop:before{content:""}.codicon-debug:before{content:""}.codicon-device-camera-video:before{content:""}.codicon-device-camera:before{content:""}.codicon-device-mobile:before{content:""}.codicon-diff-added:before{content:""}.codicon-diff-ignored:before{content:""}.codicon-diff-modified:before{content:""}.codicon-diff-removed:before{content:""}.codicon-diff-renamed:before{content:""}.codicon-diff:before{content:""}.codicon-diff-sidebyside:before{content:""}.codicon-discard:before{content:""}.codicon-editor-layout:before{content:""}.codicon-empty-window:before{content:""}.codicon-exclude:before{content:""}.codicon-extensions:before{content:""}.codicon-eye-closed:before{content:""}.codicon-file-binary:before{content:""}.codicon-file-code:before{content:""}.codicon-file-media:before{content:""}.codicon-file-pdf:before{content:""}.codicon-file-submodule:before{content:""}.codicon-file-symlink-directory:before{content:""}.codicon-file-symlink-file:before{content:""}.codicon-file-zip:before{content:""}.codicon-files:before{content:""}.codicon-filter:before{content:""}.codicon-flame:before{content:""}.codicon-fold-down:before{content:""}.codicon-fold-up:before{content:""}.codicon-fold:before{content:""}.codicon-folder-active:before{content:""}.codicon-folder-opened:before{content:""}.codicon-gear:before{content:""}.codicon-gift:before{content:""}.codicon-gist-secret:before{content:""}.codicon-gist:before{content:""}.codicon-git-commit:before{content:""}.codicon-git-compare:before{content:""}.codicon-compare-changes:before{content:""}.codicon-git-merge:before{content:""}.codicon-github-action:before{content:""}.codicon-github-alt:before{content:""}.codicon-globe:before{content:""}.codicon-grabber:before{content:""}.codicon-graph:before{content:""}.codicon-gripper:before{content:""}.codicon-heart:before{content:""}.codicon-home:before{content:""}.codicon-horizontal-rule:before{content:""}.codicon-hubot:before{content:""}.codicon-inbox:before{content:""}.codicon-issue-reopened:before{content:""}.codicon-issues:before{content:""}.codicon-italic:before{content:""}.codicon-jersey:before{content:""}.codicon-json:before{content:""}.codicon-kebab-vertical:before{content:""}.codicon-key:before{content:""}.codicon-law:before{content:""}.codicon-lightbulb-autofix:before{content:""}.codicon-link-external:before{content:""}.codicon-link:before{content:""}.codicon-list-ordered:before{content:""}.codicon-list-unordered:before{content:""}.codicon-live-share:before{content:""}.codicon-loading:before{content:""}.codicon-location:before{content:""}.codicon-mail-read:before{content:""}.codicon-mail:before{content:""}.codicon-markdown:before{content:""}.codicon-megaphone:before{content:""}.codicon-mention:before{content:""}.codicon-milestone:before{content:""}.codicon-git-pull-request-milestone:before{content:""}.codicon-mortar-board:before{content:""}.codicon-move:before{content:""}.codicon-multiple-windows:before{content:""}.codicon-mute:before{content:""}.codicon-no-newline:before{content:""}.codicon-note:before{content:""}.codicon-octoface:before{content:""}.codicon-open-preview:before{content:""}.codicon-package:before{content:""}.codicon-paintcan:before{content:""}.codicon-pin:before{content:""}.codicon-play:before{content:""}.codicon-run:before{content:""}.codicon-plug:before{content:""}.codicon-preserve-case:before{content:""}.codicon-preview:before{content:""}.codicon-project:before{content:""}.codicon-pulse:before{content:""}.codicon-question:before{content:""}.codicon-quote:before{content:""}.codicon-radio-tower:before{content:""}.codicon-reactions:before{content:""}.codicon-references:before{content:""}.codicon-refresh:before{content:""}.codicon-regex:before{content:""}.codicon-remote-explorer:before{content:""}.codicon-remote:before{content:""}.codicon-remove:before{content:""}.codicon-replace-all:before{content:""}.codicon-replace:before{content:""}.codicon-repo-clone:before{content:""}.codicon-repo-force-push:before{content:""}.codicon-repo-pull:before{content:""}.codicon-repo-push:before{content:""}.codicon-report:before{content:""}.codicon-request-changes:before{content:""}.codicon-rocket:before{content:""}.codicon-root-folder-opened:before{content:""}.codicon-root-folder:before{content:""}.codicon-rss:before{content:""}.codicon-ruby:before{content:""}.codicon-save-all:before{content:""}.codicon-save-as:before{content:""}.codicon-save:before{content:""}.codicon-screen-full:before{content:""}.codicon-screen-normal:before{content:""}.codicon-search-stop:before{content:""}.codicon-server:before{content:""}.codicon-settings-gear:before{content:""}.codicon-settings:before{content:""}.codicon-shield:before{content:""}.codicon-smiley:before{content:""}.codicon-sort-precedence:before{content:""}.codicon-split-horizontal:before{content:""}.codicon-split-vertical:before{content:""}.codicon-squirrel:before{content:""}.codicon-star-full:before{content:""}.codicon-star-half:before{content:""}.codicon-symbol-class:before{content:""}.codicon-symbol-color:before{content:""}.codicon-symbol-constant:before{content:""}.codicon-symbol-enum-member:before{content:""}.codicon-symbol-field:before{content:""}.codicon-symbol-file:before{content:""}.codicon-symbol-interface:before{content:""}.codicon-symbol-keyword:before{content:""}.codicon-symbol-misc:before{content:""}.codicon-symbol-operator:before{content:""}.codicon-symbol-property:before{content:""}.codicon-wrench:before{content:""}.codicon-wrench-subaction:before{content:""}.codicon-symbol-snippet:before{content:""}.codicon-tasklist:before{content:""}.codicon-telescope:before{content:""}.codicon-text-size:before{content:""}.codicon-three-bars:before{content:""}.codicon-thumbsdown:before{content:""}.codicon-thumbsup:before{content:""}.codicon-tools:before{content:""}.codicon-triangle-down:before{content:""}.codicon-triangle-left:before{content:""}.codicon-triangle-right:before{content:""}.codicon-triangle-up:before{content:""}.codicon-twitter:before{content:""}.codicon-unfold:before{content:""}.codicon-unlock:before{content:""}.codicon-unmute:before{content:""}.codicon-unverified:before{content:""}.codicon-verified:before{content:""}.codicon-versions:before{content:""}.codicon-vm-active:before{content:""}.codicon-vm-outline:before{content:""}.codicon-vm-running:before{content:""}.codicon-watch:before{content:""}.codicon-whitespace:before{content:""}.codicon-whole-word:before{content:""}.codicon-window:before{content:""}.codicon-word-wrap:before{content:""}.codicon-zoom-in:before{content:""}.codicon-zoom-out:before{content:""}.codicon-list-filter:before{content:""}.codicon-list-flat:before{content:""}.codicon-list-selection:before{content:""}.codicon-selection:before{content:""}.codicon-list-tree:before{content:""}.codicon-debug-breakpoint-function-unverified:before{content:""}.codicon-debug-breakpoint-function:before{content:""}.codicon-debug-breakpoint-function-disabled:before{content:""}.codicon-debug-stackframe-active:before{content:""}.codicon-circle-small-filled:before{content:""}.codicon-debug-stackframe-dot:before{content:""}.codicon-terminal-decoration-mark:before{content:""}.codicon-debug-stackframe:before{content:""}.codicon-debug-stackframe-focused:before{content:""}.codicon-debug-breakpoint-unsupported:before{content:""}.codicon-symbol-string:before{content:""}.codicon-debug-reverse-continue:before{content:""}.codicon-debug-step-back:before{content:""}.codicon-debug-restart-frame:before{content:""}.codicon-debug-alt:before{content:""}.codicon-call-incoming:before{content:""}.codicon-call-outgoing:before{content:""}.codicon-menu:before{content:""}.codicon-expand-all:before{content:""}.codicon-feedback:before{content:""}.codicon-git-pull-request-reviewer:before{content:""}.codicon-group-by-ref-type:before{content:""}.codicon-ungroup-by-ref-type:before{content:""}.codicon-account:before{content:""}.codicon-git-pull-request-assignee:before{content:""}.codicon-bell-dot:before{content:""}.codicon-debug-console:before{content:""}.codicon-library:before{content:""}.codicon-output:before{content:""}.codicon-run-all:before{content:""}.codicon-sync-ignored:before{content:""}.codicon-pinned:before{content:""}.codicon-github-inverted:before{content:""}.codicon-server-process:before{content:""}.codicon-server-environment:before{content:""}.codicon-pass:before{content:""}.codicon-issue-closed:before{content:""}.codicon-stop-circle:before{content:""}.codicon-play-circle:before{content:""}.codicon-record:before{content:""}.codicon-debug-alt-small:before{content:""}.codicon-vm-connect:before{content:""}.codicon-cloud:before{content:""}.codicon-merge:before{content:""}.codicon-export:before{content:""}.codicon-graph-left:before{content:""}.codicon-magnet:before{content:""}.codicon-notebook:before{content:""}.codicon-redo:before{content:""}.codicon-check-all:before{content:""}.codicon-pinned-dirty:before{content:""}.codicon-pass-filled:before{content:""}.codicon-circle-large-filled:before{content:""}.codicon-circle-large:before{content:""}.codicon-circle-large-outline:before{content:""}.codicon-combine:before{content:""}.codicon-gather:before{content:""}.codicon-table:before{content:""}.codicon-variable-group:before{content:""}.codicon-type-hierarchy:before{content:""}.codicon-type-hierarchy-sub:before{content:""}.codicon-type-hierarchy-super:before{content:""}.codicon-git-pull-request-create:before{content:""}.codicon-run-above:before{content:""}.codicon-run-below:before{content:""}.codicon-notebook-template:before{content:""}.codicon-debug-rerun:before{content:""}.codicon-workspace-trusted:before{content:""}.codicon-workspace-untrusted:before{content:""}.codicon-workspace-unknown:before{content:""}.codicon-terminal-cmd:before{content:""}.codicon-terminal-debian:before{content:""}.codicon-terminal-linux:before{content:""}.codicon-terminal-powershell:before{content:""}.codicon-terminal-tmux:before{content:""}.codicon-terminal-ubuntu:before{content:""}.codicon-terminal-bash:before{content:""}.codicon-arrow-swap:before{content:""}.codicon-copy:before{content:""}.codicon-person-add:before{content:""}.codicon-filter-filled:before{content:""}.codicon-wand:before{content:""}.codicon-debug-line-by-line:before{content:""}.codicon-inspect:before{content:""}.codicon-layers:before{content:""}.codicon-layers-dot:before{content:""}.codicon-layers-active:before{content:""}.codicon-compass:before{content:""}.codicon-compass-dot:before{content:""}.codicon-compass-active:before{content:""}.codicon-azure:before{content:""}.codicon-issue-draft:before{content:""}.codicon-git-pull-request-closed:before{content:""}.codicon-git-pull-request-draft:before{content:""}.codicon-debug-all:before{content:""}.codicon-debug-coverage:before{content:""}.codicon-run-errors:before{content:""}.codicon-folder-library:before{content:""}.codicon-debug-continue-small:before{content:""}.codicon-beaker-stop:before{content:""}.codicon-graph-line:before{content:""}.codicon-graph-scatter:before{content:""}.codicon-pie-chart:before{content:""}.codicon-bracket:before{content:""}.codicon-bracket-dot:before{content:""}.codicon-bracket-error:before{content:""}.codicon-lock-small:before{content:""}.codicon-azure-devops:before{content:""}.codicon-verified-filled:before{content:""}.codicon-newline:before{content:""}.codicon-layout:before{content:""}.codicon-layout-activitybar-left:before{content:""}.codicon-layout-activitybar-right:before{content:""}.codicon-layout-panel-left:before{content:""}.codicon-layout-panel-center:before{content:""}.codicon-layout-panel-justify:before{content:""}.codicon-layout-panel-right:before{content:""}.codicon-layout-panel:before{content:""}.codicon-layout-sidebar-left:before{content:""}.codicon-layout-sidebar-right:before{content:""}.codicon-layout-statusbar:before{content:""}.codicon-layout-menubar:before{content:""}.codicon-layout-centered:before{content:""}.codicon-target:before{content:""}.codicon-indent:before{content:""}.codicon-record-small:before{content:""}.codicon-error-small:before{content:""}.codicon-terminal-decoration-error:before{content:""}.codicon-arrow-circle-down:before{content:""}.codicon-arrow-circle-left:before{content:""}.codicon-arrow-circle-right:before{content:""}.codicon-arrow-circle-up:before{content:""}.codicon-layout-sidebar-right-off:before{content:""}.codicon-layout-panel-off:before{content:""}.codicon-layout-sidebar-left-off:before{content:""}.codicon-blank:before{content:""}.codicon-heart-filled:before{content:""}.codicon-map:before{content:""}.codicon-map-horizontal:before{content:""}.codicon-fold-horizontal:before{content:""}.codicon-map-filled:before{content:""}.codicon-map-horizontal-filled:before{content:""}.codicon-fold-horizontal-filled:before{content:""}.codicon-circle-small:before{content:""}.codicon-bell-slash:before{content:""}.codicon-bell-slash-dot:before{content:""}.codicon-comment-unresolved:before{content:""}.codicon-git-pull-request-go-to-changes:before{content:""}.codicon-git-pull-request-new-changes:before{content:""}.codicon-search-fuzzy:before{content:""}.codicon-comment-draft:before{content:""}.codicon-send:before{content:""}.codicon-sparkle:before{content:""}.codicon-insert:before{content:""}.codicon-mic:before{content:""}.codicon-thumbsdown-filled:before{content:""}.codicon-thumbsup-filled:before{content:""}.codicon-coffee:before{content:""}.codicon-snake:before{content:""}.codicon-game:before{content:""}.codicon-vr:before{content:""}.codicon-chip:before{content:""}.codicon-piano:before{content:""}.codicon-music:before{content:""}.codicon-mic-filled:before{content:""}.codicon-repo-fetch:before{content:""}.codicon-copilot:before{content:""}.codicon-lightbulb-sparkle:before{content:""}.codicon-robot:before{content:""}.codicon-sparkle-filled:before{content:""}.codicon-diff-single:before{content:""}.codicon-diff-multiple:before{content:""}.codicon-surround-with:before{content:""}.codicon-share:before{content:""}.codicon-git-stash:before{content:""}.codicon-git-stash-apply:before{content:""}.codicon-git-stash-pop:before{content:""}.codicon-vscode:before{content:""}.codicon-vscode-insiders:before{content:""}.codicon-code-oss:before{content:""}.codicon-run-coverage:before{content:""}.codicon-run-all-coverage:before{content:""}.codicon-coverage:before{content:""}.codicon-github-project:before{content:""}.codicon-map-vertical:before{content:""}.codicon-fold-vertical:before{content:""}.codicon-map-vertical-filled:before{content:""}.codicon-fold-vertical-filled:before{content:""}.codicon-go-to-search:before{content:""}.codicon-percentage:before{content:""}.codicon-sort-percentage:before{content:""}.codicon-git-fetch:before{content:""}
